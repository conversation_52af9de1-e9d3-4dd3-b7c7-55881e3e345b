@extends('frontend.suruchi.layouts.app')

@if (isset($category_id))
    @php
        $meta_title = $category->meta_title;
        $meta_description = $category->meta_description;
    @endphp
@elseif (isset($brand_id))
    @php
        $meta_title = get_single_brand($brand_id)->meta_title;
        $meta_description = get_single_brand($brand_id)->meta_description;
    @endphp
@else
    @php
        $meta_title         = get_setting('meta_title');
        $meta_description   = get_setting('meta_description');
    @endphp
@endif

@php
    $product_count = get_products_count()
@endphp

@section('meta_title'){{ $meta_title }}@stop
@section('meta_description'){{ $meta_description }}@stop

@section('meta')
    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="{{ $meta_title }}">
    <meta itemprop="description" content="{{ $meta_description }}">

    <!-- Twitter Card data -->
    <meta name="twitter:title" content="{{ $meta_title }}">
    <meta name="twitter:description" content="{{ $meta_description }}">

    <!-- Open Graph data -->
    <meta property="og:title" content="{{ $meta_title }}" />
    <meta property="og:description" content="{{ $meta_description }}" />
@endsection

@section('content')

    {{-- <section class="shop__section section--padding"> --}}
    <section class="shop__section">
        <div class="container-fluid">
            <form class="" id="search-form" action="" method="GET">
                @if(request('keyword'))
                    <input type="hidden" name="keyword" value="{{ request('keyword') }}">
                @endif
                @if(request('location'))
                    <input type="hidden" name="location" value="{{ request('location') }}">
                @endif
                @if(request('latitude'))
                        <input type="hidden" name="latitude" value="{{ request('latitude') }}">
                    @endif
                    @if(request('longitude'))
                        <input type="hidden" name="longitude" value="{{ request('longitude') }}">
                    @endif
                    @if(request('rating_filter'))
                        <input type="hidden" name="rating_filter" value="{{ request('rating_filter') }}">
                    @endif
                    @if(request('discount_filter'))
                        <input type="hidden" name="discount_filter" value="{{ request('discount_filter') }}">
                    @endif
                    @if(request('availability_filter'))
                        <input type="hidden" name="availability_filter" value="{{ request('availability_filter') }}">
                    @endif
                    @if(request('delivery_filter'))
                        <input type="hidden" name="delivery_filter" value="{{ request('delivery_filter') }}">
                    @endif
                    @if(request('seller_type_filter'))
                        <input type="hidden" name="seller_type_filter" value="{{ request('seller_type_filter') }}">
                    @endif
                    @if(request('deal_type_filter'))
                        <input type="hidden" name="deal_type_filter" value="{{ request('deal_type_filter') }}">
                    @endif
                    @if(request('review_count_filter'))
                        <input type="hidden" name="review_count_filter" value="{{ request('review_count_filter') }}">
                    @endif
                <div class="shop__header bg__gray--color d-flex align-items-center justify-content-between mb-30">
                    <button type="button" class="widget__filter--btn d-flex d-lg-none align-items-center" data-offcanvas="">
                        <svg class="widget__filter--btn__icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28" d="M368 128h80M64 128h240M368 384h80M64 384h240M208 256h240M64 256h80"></path><circle cx="336" cy="128" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle><circle cx="176" cy="256" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle><circle cx="336" cy="384" r="28" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="28"></circle></svg>
                        <span class="widget__filter--btn__text">{{ translate('Filters') }}</span>
                    </button>
                    <div class="product__view--mode d-flex align-items-center">
                        <div class="product__view--mode__list product__short--by align-items-center d-none d-lg-flex">
                            <label class="product__view--label">Sort By :</label>
                            <div class="select shop__header--select">
                                <select class="product__view--select" name="sort_by" onchange="filter()">
                                    <option value="">{{ translate('Sort by')}}</option>
                                    <option value="newest" @isset($sort_by) @if ($sort_by == 'newest') selected @endif @endisset>{{ translate('Newest')}}</option>
                                    <option value="oldest" @isset($sort_by) @if ($sort_by == 'oldest') selected @endif @endisset>{{ translate('Oldest')}}</option>
                                    <option value="price-asc" @isset($sort_by) @if ($sort_by == 'price-asc') selected @endif @endisset>{{ translate('Price low to high')}}</option>
                                    <option value="price-desc" @isset($sort_by) @if ($sort_by == 'price-desc') selected @endif @endisset>{{ translate('Price high to low')}}</option>
                                    <option value="best-selling" @isset($sort_by) @if ($sort_by == 'best-selling') selected @endif @endisset>{{ translate('Best Selling')}}</option>
                                    <option value="top-rated" @isset($sort_by) @if ($sort_by == 'top-rated') selected @endif @endisset>{{ translate('Top Rated')}}</option>
                                    <option value="most-reviewed" @isset($sort_by) @if ($sort_by == 'most-reviewed') selected @endif @endisset>{{ translate('Most Reviewed')}}</option>
                                    <option value="featured" @isset($sort_by) @if ($sort_by == 'featured') selected @endif @endisset>{{ translate('Featured')}}</option>
                                    <option value="discount-high" @isset($sort_by) @if ($sort_by == 'discount-high') selected @endif @endisset>{{ translate('Highest Discount')}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="product__view--mode__list">
                            <div class="product__grid--column__buttons d-flex justify-content-center">
                                <button type="button" class="product__grid--column__buttons--icons active" aria-label="product grid button" data-toggle="tab" data-target="#product_grid">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 9 9">
                                        <g transform="translate(-1360 -479)">
                                            <rect id="Rectangle_5725" data-name="Rectangle 5725" width="4" height="4" transform="translate(1360 479)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5727" data-name="Rectangle 5727" width="4" height="4" transform="translate(1360 484)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5726" data-name="Rectangle 5726" width="4" height="4" transform="translate(1365 479)" fill="currentColor"></rect>
                                            <rect id="Rectangle_5728" data-name="Rectangle 5728" width="4" height="4" transform="translate(1365 484)" fill="currentColor"></rect>
                                        </g>
                                    </svg>
                                </button>
                                <button type="button" class="product__grid--column__buttons--icons" aria-label="product list button" data-toggle="tab" data-target="#product_list">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 13 8">
                                        <g id="Group_14700" data-name="Group 14700" transform="translate(-1376 -478)">
                                            <g transform="translate(12 -2)">
                                                <g id="Group_1326" data-name="Group 1326">
                                                    <rect id="Rectangle_5729" data-name="Rectangle 5729" width="3" height="2" transform="translate(1364 483)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5730" data-name="Rectangle 5730" width="9" height="2" transform="translate(1368 483)" fill="currentColor"></rect>
                                                </g>
                                                <g id="Group_1328" data-name="Group 1328" transform="translate(0 -3)">
                                                    <rect id="Rectangle_5729-2" data-name="Rectangle 5729" width="3" height="2" transform="translate(1364 483)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5730-2" data-name="Rectangle 5730" width="9" height="2" transform="translate(1368 483)" fill="currentColor"></rect>
                                                </g>
                                                <g id="Group_1327" data-name="Group 1327" transform="translate(0 -1)">
                                                    <rect id="Rectangle_5731" data-name="Rectangle 5731" width="3" height="2" transform="translate(1364 487)" fill="currentColor"></rect>
                                                    <rect id="Rectangle_5732" data-name="Rectangle 5732" width="9" height="2" transform="translate(1368 487)" fill="currentColor"></rect>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        @if (get_setting('vendor_system_activation') == 1)
                            <div class="product__view--mode__list product__short--by align-items-center d-none d-lg-flex">
                                <label class="product__view--label">Search By :</label>
                                <div class="select shop__header--select">
                                    <select class="product__view--select" name="search_by" onchange="filter()">
                                        <option value="products" @isset($sort_by) @if ($sort_by == 'products') selected @endif @endisset>{{ translate('Products')}}</option>
                                        <option value="shops" @isset($sort_by) @if ($sort_by == 'shops') selected @endif @endisset>{{ translate('Shops')}}</option>
                                    </select>
                                </div>
                            </div>
                        @endif
                    </div>
                    {{--                    <p class="product__showing--count">Showing 1–9 of 21 results</p>--}}
                </div>
                <div class="row">
                    <div class="col-xl-3 col-lg-4">
                        <div class="shop__sidebar--widget widget__area d-none d-lg-block">
                            <div class="single__widget widget__bg">
                                <h2 class="widget__title h3">{{ translate('Categories')}}</h2>
                                <ul class="widget__categories--menu">
                                    @if (!isset($category_id))
                                        @foreach ($categories as $category)
                                            <a href="{{ route('products.category', $category->slug) }}">
                                                <li class="widget__categories--menu__list">
                                                    <label class="widget__categories--menu__label d-flex align-items-center">
                                                        <span class="widget__categories--menu__text">{{ $category->getTranslation('name') }}</span>
                                                    </label>
                                                </li>
                                            </a>
                                        @endforeach
                                    @else
                                        <a class="text-reset fs-14 w-100 hov-text-primary" href="{{ route('search') }}">
                                            <li class="widget__categories--menu__list py-2 px-4">
                                                <i class="las la-angle-left"></i>
                                                {{ translate('All Categories')}}
                                            </li>
                                        </a>

                                        <li class="widget__categories--menu__list">
                                            <label class="widget__categories--menu__label d-flex align-items-center">
                                                <span class="widget__categories--menu__text">{{ $category->getTranslation('name') }}</span>
                                                @if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0)
                                                    <svg class="widget__categories--menu__arrowdown--icon" xmlns="http://www.w3.org/2000/svg" width="12.355" height="8.394">
                                                        <path d="M15.138,8.59l-3.961,3.952L7.217,8.59,6,9.807l5.178,5.178,5.178-5.178Z" transform="translate(-6 -8.59)" fill="currentColor"></path>
                                                    </svg>
                                                @endif
                                            </label>
                                            @if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0)
                                                <ul class="widget__categories--sub__menu" style="display: none; box-sizing: border-box;">
                                                    @foreach ($category->childrenCategories as $key => $immediate_children_category)
                                                        <li class="widget__categories--sub__menu--list">
                                                            <a class="widget__categories--sub__menu--link d-flex align-items-center" href="{{ route('products.category', $immediate_children_category->slug) }}">
                                                                <span class="widget__categories--sub__menu--text">{{ $immediate_children_category->getTranslation('name') }}</span>
                                                            </a>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            @endif
                                        </li>
                                    @endif

                                </ul>
                            </div>

                            <!-- Clear Filters Button -->
                            <div class="single__widget widget__bg">
                                <div class="text-center p-3">
                                    <button type="button" class="primary__btn w-100" onclick="clearAllFilters()">
                                        <i class="las la-times"></i>
                                        {{ translate('Clear All Filters')}}
                                    </button>
                                </div>
                            </div>

                            <!-- Filter Stats -->
                            @if(isset($filter_stats))
                            <div class="single__widget widget__bg">
                                <h2 class="widget__title h3">{{ translate('Quick Stats')}}</h2>
                                <div class="p-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fs-14 text-muted">{{ translate('Total Products')}}</span>
                                        <span class="fs-14 fw-600">{{ number_format($filter_stats['total_products']) }}</span>
                                    </div>
                                    @if($filter_stats['avg_rating'] > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fs-14 text-muted">{{ translate('Avg Rating')}}</span>
                                        <span class="fs-14 fw-600">
                                            {{ $filter_stats['avg_rating'] }}
                                            <i class="las la-star text-warning"></i>
                                        </span>
                                    </div>
                                    @endif
                                    @if($filter_stats['discount_products'] > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fs-14 text-muted">{{ translate('On Sale')}}</span>
                                        <span class="fs-14 fw-600 text-danger">{{ number_format($filter_stats['discount_products']) }}</span>
                                    </div>
                                    @endif
                                    @if($filter_stats['free_shipping_products'] > 0)
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="fs-14 text-muted">{{ translate('Free Shipping')}}</span>
                                        <span class="fs-14 fw-600 text-success">{{ number_format($filter_stats['free_shipping_products']) }}</span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endif

                            <!-- Top Brands Filter -->
                            @if(isset($top_brands) && $top_brands->count() > 0)
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_brands">
                                            {{ translate('Top Brands')}}
                                        </a>
                                    </div>
                                    <div class="collapse" id="collapse_brands">
                                        <div class="p-3 aiz-checkbox-list">
                                            @foreach($top_brands as $brand)
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="brand_filter" value="{{ $brand->id }}"
                                                       @if(request('brand') == $brand->slug || request('brand_filter') == $brand->id) checked @endif
                                                       onchange="filterByBrand({{ $brand->id }}, '{{ $brand->slug }}')">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ $brand->getTranslation('name') }}</span>
                                            </label>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Attributes -->
                            @foreach ($attributes as $attribute)
                                <div class="single__widget widget__bg">
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between"
                                               data-toggle="collapse" data-target="#collapse_{{ str_replace(' ', '_', $attribute->name) }}" style="white-space: normal;">
                                                {{ $attribute->getTranslation('name') }}
                                            </a>
                                        </div>
                                        @php
                                            $show = '';
                                            foreach ($attribute->attribute_values as $attribute_value){
                                                if(in_array($attribute_value->value, $selected_attribute_values)){
                                                    $show = 'show';
                                                }
                                            }
                                        @endphp
                                        <div class="collapse {{ $show }}" id="collapse_{{ str_replace(' ', '_', $attribute->name) }}">
                                            <div class="p-3 aiz-checkbox-list">
                                                @foreach ($attribute->attribute_values as $attribute_value)
                                                    <label class="aiz-checkbox mb-3">
                                                        <input
                                                            type="checkbox"
                                                            name="selected_attribute_values[]"
                                                            value="{{ $attribute_value->value }}" @if (in_array($attribute_value->value, $selected_attribute_values)) checked @endif
                                                            onchange="filter()"
                                                        >
                                                        <span class="aiz-square-check"></span>
                                                        <span class="fs-14 fw-400 text-dark">{{ $attribute_value->value }}</span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <!-- Color -->
                            @if (get_setting('color_filter_activation'))
                                <div class="single__widget widget__bg">
                                    <div class="bg-white border mb-3">
                                        <div class="fs-16 fw-700 p-3">
                                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_color">
                                                {{ translate('Filter by color')}}
                                            </a>
                                        </div>
                                        @php
                                            $show = '';
                                            foreach ($colors as $key => $color){
                                                if(isset($selected_color) && $selected_color == $color->code){
                                                    $show = 'show';
                                                }
                                            }
                                        @endphp
                                        <div class="collapse {{ $show }}" id="collapse_color">
                                            <div class="p-3 aiz-radio-inline">
                                                @foreach ($colors as $key => $color)
                                                    <label class="aiz-megabox pl-0 mr-2" data-toggle="tooltip" data-title="{{ $color->name }}">
                                                        <input
                                                            type="radio"
                                                            name="color"
                                                            value="{{ $color->code }}"
                                                            onchange="filter()"
                                                            @if(isset($selected_color) && $selected_color == $color->code) checked @endif
                                                        >
                                                        <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center p-1 mb-2">
                                                        <span class="size-30px d-inline-block rounded" style="background: {{ $color->code }};"></span>
                                                    </span>
                                                    </label>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="single__widget price__filter widget__bg">
                                <h2 class="widget__title h3">Filter By Price</h2>
                                <form class="price__filter--form" action="#">
                                    <div class="price__filter--form__inner mb-15 d-flex align-items-center">
                                        <div class="price__filter--group">
                                            <label class="price__filter--label" for="Filter-Price-GTE2">From</label>
                                            <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                                <span class="price__filter--currency">₹</span>
                                                <label>
                                                    <input class="price__filter--input__field border-0" name="min_price" value="@if(isset($min_price)){{ $min_price }}@endif" type="number" placeholder="0"
                                                           @if($products->min('unit_price') > 0)
                                                               min="{{ $products->min('unit_price') }}"
                                                           @else
                                                               min="0"
                                                           @endif
                                                           @if($products->max('unit_price') > 0)
                                                               max="{{ $products->max('unit_price') }}"
                                                           @else
                                                               max="0"
                                                        @endif
                                                    >
                                                </label>
                                            </div>
                                        </div>
                                        <div class="price__divider">
                                            <span>-</span>
                                        </div>
                                        <div class="price__filter--group">
                                            <label class="price__filter--label" for="Filter-Price-LTE2">To</label>
                                            <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                                <span class="price__filter--currency">₹</span>
                                                <label>
                                                    <input class="price__filter--input__field border-0" name="max_price" value="@if(isset($max_price)){{ $max_price }}@endif" type="number" placeholder="999.00"
                                                           @if($products->min('unit_price') > 0)
                                                               min="{{ $products->min('unit_price') }}"
                                                           @else
                                                               min="0"
                                                           @endif
                                                           @if($products->max('unit_price') > 0)
                                                               max="{{ $products->max('unit_price') }}"
                                                           @else
                                                               max="0"
                                                        @endif
                                                    >
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="price__filter--btn primary__btn" type="submit">Filter</button>
                                </form>

                                <!-- Smart Price Ranges -->
                                @if(isset($smart_price_ranges))
                                <div class="mt-3">
                                    <h6 class="fs-14 fw-600 mb-2">{{ translate('Quick Price Ranges')}}</h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        @foreach($smart_price_ranges as $range)
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                onclick="setQuickPriceRange({{ $range['min'] }}, {{ $range['max'] ?? 'null' }})">
                                            {{ $range['label'] }}
                                        </button>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Enhanced Rating Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_rating">
                                            {{ translate('Customer Rating')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($rating_filter)) show @endif" id="collapse_rating">
                                        <div class="p-3 aiz-radio-list">
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="rating_filter" value="4_plus" @if(isset($rating_filter) && $rating_filter == '4_plus') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    {{ translate('4 Stars & Up')}}
                                                </span>
                                            </label>
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="rating_filter" value="3_plus" @if(isset($rating_filter) && $rating_filter == '3_plus') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-muted"></i>
                                                    {{ translate('3 Stars & Up')}}
                                                </span>
                                            </label>
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="rating_filter" value="2_plus" @if(isset($rating_filter) && $rating_filter == '2_plus') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-warning"></i>
                                                    <i class="las la-star text-muted"></i>
                                                    <i class="las la-star text-muted"></i>
                                                    {{ translate('2 Stars & Up')}}
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Discount Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_discount">
                                            {{ translate('Discount Range')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($discount_filter)) show @endif" id="collapse_discount">
                                        <div class="p-3 aiz-checkbox-list">
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="discount_filter" value="10_25" @if(isset($discount_filter) && $discount_filter == '10_25') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('10% - 25% Off')}}</span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="discount_filter" value="25_50" @if(isset($discount_filter) && $discount_filter == '25_50') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('25% - 50% Off')}}</span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="discount_filter" value="50_plus" @if(isset($discount_filter) && $discount_filter == '50_plus') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('50% & More Off')}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Availability & Features Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_availability">
                                            {{ translate('Availability & Features')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($availability_filter)) show @endif" id="collapse_availability">
                                        <div class="p-3 aiz-checkbox-list">
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="availability_filter" value="in_stock" @if(isset($availability_filter) && $availability_filter == 'in_stock') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('In Stock Only')}}</span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="availability_filter" value="free_shipping" @if(isset($availability_filter) && $availability_filter == 'free_shipping') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('Free Shipping')}}</span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="availability_filter" value="cod_available" @if(isset($availability_filter) && $availability_filter == 'cod_available') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('Cash on Delivery')}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Speed Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_delivery">
                                            {{ translate('Delivery Speed')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($delivery_filter)) show @endif" id="collapse_delivery">
                                        <div class="p-3 aiz-radio-list">
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="delivery_filter" value="express" @if(isset($delivery_filter) && $delivery_filter == 'express') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('Express (1-2 Days)')}}</span>
                                            </label>
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="delivery_filter" value="fast" @if(isset($delivery_filter) && $delivery_filter == 'fast') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">{{ translate('Fast (3-5 Days)')}}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Seller Type Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_seller">
                                            {{ translate('Seller Type')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($seller_type_filter)) show @endif" id="collapse_seller">
                                        <div class="p-3 aiz-radio-list">
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="seller_type_filter" value="verified" @if(isset($seller_type_filter) && $seller_type_filter == 'verified') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-check-circle text-success"></i>
                                                    {{ translate('Verified Sellers')}}
                                                </span>
                                            </label>
                                            <label class="aiz-radio mb-3">
                                                <input type="radio" name="seller_type_filter" value="admin" @if(isset($seller_type_filter) && $seller_type_filter == 'admin') checked @endif onchange="filter()">
                                                <span class="aiz-rounded-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-crown text-warning"></i>
                                                    {{ translate('Official Store')}}
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Deal Type Filter -->
                            <div class="single__widget widget__bg">
                                <div class="bg-white border mb-3">
                                    <div class="fs-16 fw-700 p-3">
                                        <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_deals">
                                            {{ translate('Special Deals')}}
                                        </a>
                                    </div>
                                    <div class="collapse @if(isset($deal_type_filter)) show @endif" id="collapse_deals">
                                        <div class="p-3 aiz-checkbox-list">
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="deal_type_filter" value="todays_deal" @if(isset($deal_type_filter) && $deal_type_filter == 'todays_deal') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-fire text-danger"></i>
                                                    {{ translate("Today's Deal")}}
                                                </span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="deal_type_filter" value="featured" @if(isset($deal_type_filter) && $deal_type_filter == 'featured') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-star text-warning"></i>
                                                    {{ translate('Featured Products')}}
                                                </span>
                                            </label>
                                            <label class="aiz-checkbox mb-3">
                                                <input type="checkbox" name="deal_type_filter" value="flash_deal" @if(isset($deal_type_filter) && $deal_type_filter == 'flash_deal') checked @endif onchange="filter()">
                                                <span class="aiz-square-check"></span>
                                                <span class="fs-14 fw-400 text-dark">
                                                    <i class="las la-bolt text-primary"></i>
                                                    {{ translate('Flash Deals')}}
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-9 col-lg-8">
                        <!-- Active Filters Summary -->
                        @php
                            $active_filters = [];
                            if(request('min_price') || request('max_price')) {
                                $price_text = '₹' . (request('min_price') ?: '0') . ' - ₹' . (request('max_price') ?: 'Max');
                                $active_filters[] = ['type' => 'price', 'label' => 'Price: ' . $price_text];
                            }
                            if(request('rating_filter')) {
                                $rating_labels = ['4_plus' => '4+ Stars', '3_plus' => '3+ Stars', '2_plus' => '2+ Stars'];
                                $active_filters[] = ['type' => 'rating', 'label' => $rating_labels[request('rating_filter')] ?? ''];
                            }
                            if(request('discount_filter')) {
                                $discount_labels = ['10_25' => '10-25% Off', '25_50' => '25-50% Off', '50_plus' => '50%+ Off'];
                                $active_filters[] = ['type' => 'discount', 'label' => $discount_labels[request('discount_filter')] ?? ''];
                            }
                            if(request('availability_filter')) {
                                $availability_labels = ['in_stock' => 'In Stock', 'free_shipping' => 'Free Shipping', 'cod_available' => 'COD Available'];
                                $active_filters[] = ['type' => 'availability', 'label' => $availability_labels[request('availability_filter')] ?? ''];
                            }
                            if(request('seller_type_filter')) {
                                $seller_labels = ['verified' => 'Verified Sellers', 'admin' => 'Official Store'];
                                $active_filters[] = ['type' => 'seller', 'label' => $seller_labels[request('seller_type_filter')] ?? ''];
                            }
                            if(request('deal_type_filter')) {
                                $deal_labels = ['todays_deal' => "Today's Deal", 'featured' => 'Featured', 'flash_deal' => 'Flash Deal'];
                                $active_filters[] = ['type' => 'deal', 'label' => $deal_labels[request('deal_type_filter')] ?? ''];
                            }
                        @endphp

                        @if(count($active_filters) > 0)
                        <div class="active-filters-summary mb-3 p-3 bg-light border-radius-5">
                            <div class="d-flex align-items-center flex-wrap">
                                <span class="fs-14 fw-600 me-3">{{ translate('Active Filters:') }}</span>
                                @foreach($active_filters as $filter)
                                <span class="badge bg-primary me-2 mb-2 d-flex align-items-center">
                                    {{ translate($filter['label']) }}
                                    <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;"
                                            onclick="removeFilter('{{ $filter['type'] }}')"></button>
                                </span>
                                @endforeach
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearAllFilters()">
                                    {{ translate('Clear All') }}
                                </button>
                            </div>
                        </div>
                        @endif

                        <div class="shop__product--wrapper">
                            @if($products->isNotEmpty())
                                <div class="tab_content">
                                    <div id="product_grid" class="tab_pane active show">
                                        <div class="product__section--inner product__grid--inner">
                                            <div class="row row-cols-xl-4 row-cols-lg-3 row-cols-md-3 row-cols-2 mb--n30">
                                                @foreach ($products as $key => $product)
                                                    @include('frontend.'.get_setting('homepage_select').'.partials.product_box_1',['product' => $product])
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    <div id="product_list" class="tab_pane">
                                        <div class="product__section--inner">
                                            <div class="row row-cols-1 mb--n30">
                                                @foreach ($products as $key => $product)
                                                    @include('frontend.'.get_setting('homepage_select').'.partials.product_box_listing_view',['product' => $product])
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="pagination__area bg__gray--color">
                                    <nav class="pagination justify-content-center">
                                        <ul class="pagination__wrapper d-flex align-items-center justify-content-center">
                                            {{ $products->appends(request()->input())->links() }}
                                        </ul>
                                    </nav>
                                </div>
                            @else
                                @if (get_setting('vendor_system_activation') == 1)
                                    <div class="no-products-message text-center">
                                        <h2 class="mb-3">No products available in this location, try searching for shop</h2>
                                        <a href="#" class="primary__btn quickview__cart--btn" onclick="selectShops()">
                                            {{ translate('Search by shop') }}
                                        </a>
                                    </div>
                                @else
                                    <div class="no-products-message text-center">
                                        <h2 class="mb-3">No products available for this keyword..</h2>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <div class="offcanvas__filter--sidebar widget__area">
        <button type="button" class="offcanvas__filter--close" data-offcanvas="">
            <svg class="minicart__close--icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M368 368L144 144M368 144L144 368"></path></svg> <span class="offcanvas__filter--close__text">Close</span>
        </button>
        <form class="" id="search-form" action="" method="GET">
            @if(request('keyword'))
                <input type="hidden" name="keyword" value="{{ request('keyword') }}">
            @endif
            @if(request('location'))
                <input type="hidden" name="location" value="{{ request('location') }}">
            @endif
            @if(request('latitude'))
                <input type="hidden" name="latitude" value="{{ request('latitude') }}">
            @endif
            @if(request('longitude'))
                <input type="hidden" name="longitude" value="{{ request('longitude') }}">
            @endif
            @if(request('rating_filter'))
                <input type="hidden" name="rating_filter" value="{{ request('rating_filter') }}">
            @endif
            @if(request('discount_filter'))
                <input type="hidden" name="discount_filter" value="{{ request('discount_filter') }}">
            @endif
            @if(request('availability_filter'))
                <input type="hidden" name="availability_filter" value="{{ request('availability_filter') }}">
            @endif
            @if(request('delivery_filter'))
                <input type="hidden" name="delivery_filter" value="{{ request('delivery_filter') }}">
            @endif
            @if(request('seller_type_filter'))
                <input type="hidden" name="seller_type_filter" value="{{ request('seller_type_filter') }}">
            @endif
            @if(request('deal_type_filter'))
                <input type="hidden" name="deal_type_filter" value="{{ request('deal_type_filter') }}">
            @endif
            @if(request('review_count_filter'))
                <input type="hidden" name="review_count_filter" value="{{ request('review_count_filter') }}">
            @endif
            <div class="offcanvas__filter--sidebar__inner">
            <div class="single__widget widget__bg">
                <h2 class="widget__title h3">Categories</h2>
                <ul class="widget__categories--menu">
                    @if (!isset($category_id))
                        @foreach ($categories as $category)
                            <a href="{{ route('products.category', $category->slug) }}">
                                <li class="widget__categories--menu__list">
                                    <label class="widget__categories--menu__label d-flex align-items-center">
                                        <span class="widget__categories--menu__text">{{ $category->getTranslation('name') }}</span>
                                    </label>
                                </li>
                            </a>
                        @endforeach
                    @else
                        <a class="text-reset fs-14 w-100 hov-text-primary" href="{{ route('search') }}">
                            <li class="widget__categories--menu__list py-2 px-4">
                                <i class="las la-angle-left"></i>
                                {{ translate('All Categories')}}
                            </li>
                        </a>

                        <li class="widget__categories--menu__list">
                            <label class="widget__categories--menu__label d-flex align-items-center">
                                <span class="widget__categories--menu__text">{{ $category->getTranslation('name') }}</span>
                                @if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0)
                                    <svg class="widget__categories--menu__arrowdown--icon" xmlns="http://www.w3.org/2000/svg" width="12.355" height="8.394">
                                        <path d="M15.138,8.59l-3.961,3.952L7.217,8.59,6,9.807l5.178,5.178,5.178-5.178Z" transform="translate(-6 -8.59)" fill="currentColor"></path>
                                    </svg>
                                @endif
                            </label>
                            @if(!empty($category->childrenCategories) && count($category->childrenCategories) > 0)
                                <ul class="widget__categories--sub__menu" style="display: none; box-sizing: border-box;">
                                    @foreach ($category->childrenCategories as $key => $immediate_children_category)
                                        <li class="widget__categories--sub__menu--list">
                                            <a class="widget__categories--sub__menu--link d-flex align-items-center" href="{{ route('products.category', $immediate_children_category->slug) }}">
                                                <span class="widget__categories--sub__menu--text">{{ $immediate_children_category->getTranslation('name') }}</span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </li>
                    @endif

                </ul>
            </div>
                <!-- Attributes -->
                @foreach ($attributes as $attribute)
                    <div class="single__widget widget__bg">
                        <div class="bg-white border mb-3">
                            <div class="fs-16 fw-700 p-3">
                                <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between"
                                   data-toggle="collapse" data-target="#collapse_{{ str_replace(' ', '_', $attribute->name) }}" style="white-space: normal;">
                                    {{ $attribute->getTranslation('name') }}
                                </a>
                            </div>
                            @php
                                $show = '';
                                foreach ($attribute->attribute_values as $attribute_value){
                                    if(in_array($attribute_value->value, $selected_attribute_values)){
                                        $show = 'show';
                                    }
                                }
                            @endphp
                            <div class="collapse {{ $show }}" id="collapse_{{ str_replace(' ', '_', $attribute->name) }}">
                                <div class="p-3 aiz-checkbox-list">
                                    @foreach ($attribute->attribute_values as $attribute_value)
                                        <label class="aiz-checkbox mb-3">
                                            <input
                                                type="checkbox"
                                                name="selected_attribute_values[]"
                                                value="{{ $attribute_value->value }}" @if (in_array($attribute_value->value, $selected_attribute_values)) checked @endif
                                                onchange="filter()"
                                            >
                                            <span class="aiz-square-check"></span>
                                            <span class="fs-14 fw-400 text-dark">{{ $attribute_value->value }}</span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach

                <!-- color_filter_activation -->
                @if (get_setting('color_filter_activation'))
                    <div class="single__widget widget__bg">
                        <div class="bg-white border mb-3">
                            <div class="fs-16 fw-700 p-3">
                                <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#collapse_color">
                                    {{ translate('Filter by color')}}
                                </a>
                            </div>
                            @php
                                $show = '';
                                foreach ($colors as $key => $color){
                                    if(isset($selected_color) && $selected_color == $color->code){
                                        $show = 'show';
                                    }
                                }
                            @endphp
                            <div class="collapse {{ $show }}" id="collapse_color">
                                <div class="p-3 aiz-radio-inline">
                                    @foreach ($colors as $key => $color)
                                        <label class="aiz-megabox pl-0 mr-2" data-toggle="tooltip" data-title="{{ $color->name }}">
                                            <input
                                                type="radio"
                                                name="color"
                                                value="{{ $color->code }}"
                                                onchange="filter()"
                                                @if(isset($selected_color) && $selected_color == $color->code) checked @endif
                                            >
                                            <span class="aiz-megabox-elem rounded d-flex align-items-center justify-content-center p-1 mb-2">
                                                        <span class="size-30px d-inline-block rounded" style="background: {{ $color->code }};"></span>
                                                    </span>
                                        </label>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                <div class="single__widget price__filter widget__bg">
                    <h2 class="widget__title h3">Filter By Price</h2>
                    <form class="price__filter--form" action="#">
                        <div class="price__filter--form__inner mb-15 d-flex align-items-center">
                            <div class="price__filter--group">
                                <label class="price__filter--label" for="Filter-Price-GTE2">From</label>
                                <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                    <span class="price__filter--currency">₹</span>
                                    <label>
                                        <input class="price__filter--input__field border-0" name="min_price" value="@if(isset($min_price)){{ $min_price }}@endif" type="number" placeholder="0"
                                               @if($products->min('unit_price') > 0)
                                                   min="{{ $products->min('unit_price') }}"
                                               @else
                                                   min="0"
                                               @endif
                                               @if($products->max('unit_price') > 0)
                                                   max="{{ $products->max('unit_price') }}"
                                               @else
                                                   max="0"
                                            @endif
                                        >
                                    </label>
                                </div>
                            </div>
                            <div class="price__divider">
                                <span>-</span>
                            </div>
                            <div class="price__filter--group">
                                <label class="price__filter--label" for="Filter-Price-LTE2">To</label>
                                <div class="price__filter--input border-radius-5 d-flex align-items-center">
                                    <span class="price__filter--currency">₹</span>
                                    <label>
                                        <input class="price__filter--input__field border-0" name="max_price" value="@if(isset($max_price)){{ $max_price }}@endif" type="number" placeholder="999.00"
                                               @if($products->min('unit_price') > 0)
                                                   min="{{ $products->min('unit_price') }}"
                                               @else
                                                   min="0"
                                               @endif
                                               @if($products->max('unit_price') > 0)
                                                   max="{{ $products->max('unit_price') }}"
                                               @else
                                                   max="0"
                                            @endif
                                        >
                                    </label>
                                </div>
                            </div>
                        </div>
                        <button class="price__filter--btn primary__btn" type="submit">Filter</button>
                    </form>
                </div>

                <!-- Mobile Enhanced Rating Filter -->
                <div class="single__widget widget__bg">
                    <div class="bg-white border mb-3">
                        <div class="fs-16 fw-700 p-3">
                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#mobile_collapse_rating">
                                {{ translate('Customer Rating')}}
                            </a>
                        </div>
                        <div class="collapse @if(isset($rating_filter)) show @endif" id="mobile_collapse_rating">
                            <div class="p-3 aiz-radio-list">
                                <label class="aiz-radio mb-3">
                                    <input type="radio" name="rating_filter" value="4_plus" @if(isset($rating_filter) && $rating_filter == '4_plus') checked @endif onchange="filter()">
                                    <span class="aiz-rounded-check"></span>
                                    <span class="fs-14 fw-400 text-dark">
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        {{ translate('4 Stars & Up')}}
                                    </span>
                                </label>
                                <label class="aiz-radio mb-3">
                                    <input type="radio" name="rating_filter" value="3_plus" @if(isset($rating_filter) && $rating_filter == '3_plus') checked @endif onchange="filter()">
                                    <span class="aiz-rounded-check"></span>
                                    <span class="fs-14 fw-400 text-dark">
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-warning"></i>
                                        <i class="las la-star text-muted"></i>
                                        {{ translate('3 Stars & Up')}}
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Enhanced Availability Filter -->
                <div class="single__widget widget__bg">
                    <div class="bg-white border mb-3">
                        <div class="fs-16 fw-700 p-3">
                            <a href="#" class="dropdown-toggle text-dark filter-section collapsed d-flex align-items-center justify-content-between" data-toggle="collapse" data-target="#mobile_collapse_availability">
                                {{ translate('Quick Filters')}}
                            </a>
                        </div>
                        <div class="collapse @if(isset($availability_filter)) show @endif" id="mobile_collapse_availability">
                            <div class="p-3 aiz-checkbox-list">
                                <label class="aiz-checkbox mb-3">
                                    <input type="checkbox" name="availability_filter" value="in_stock" @if(isset($availability_filter) && $availability_filter == 'in_stock') checked @endif onchange="filter()">
                                    <span class="aiz-square-check"></span>
                                    <span class="fs-14 fw-400 text-dark">{{ translate('In Stock')}}</span>
                                </label>
                                <label class="aiz-checkbox mb-3">
                                    <input type="checkbox" name="availability_filter" value="free_shipping" @if(isset($availability_filter) && $availability_filter == 'free_shipping') checked @endif onchange="filter()">
                                    <span class="aiz-square-check"></span>
                                    <span class="fs-14 fw-400 text-dark">{{ translate('Free Shipping')}}</span>
                                </label>
                                <label class="aiz-checkbox mb-3">
                                    <input type="checkbox" name="deal_type_filter" value="todays_deal" @if(isset($deal_type_filter) && $deal_type_filter == 'todays_deal') checked @endif onchange="filter()">
                                    <span class="aiz-square-check"></span>
                                    <span class="fs-14 fw-400 text-dark">{{ translate("Today's Deal")}}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection

@section('style')
<style>
    .aiz-radio, .aiz-checkbox {
        display: flex;
        align-items: center;
        cursor: pointer;
        position: relative;
    }

    .aiz-radio input[type="radio"], .aiz-checkbox input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .aiz-rounded-check, .aiz-square-check {
        height: 18px;
        width: 18px;
        border: 2px solid #ddd;
        border-radius: 50%;
        margin-right: 10px;
        position: relative;
        transition: all 0.3s ease;
    }

    .aiz-square-check {
        border-radius: 3px;
    }

    .aiz-radio input[type="radio"]:checked ~ .aiz-rounded-check,
    .aiz-checkbox input[type="checkbox"]:checked ~ .aiz-square-check {
        background-color: var(--primary-color, #007bff);
        border-color: var(--primary-color, #007bff);
    }

    .aiz-radio input[type="radio"]:checked ~ .aiz-rounded-check::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
    }

    .aiz-checkbox input[type="checkbox"]:checked ~ .aiz-square-check::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 12px;
        font-weight: bold;
    }

    .filter-section {
        text-decoration: none !important;
        color: inherit !important;
    }

    .filter-section:hover {
        color: var(--primary-color, #007bff) !important;
    }

    .btn-outline-primary {
        border: 1px solid var(--primary-color, #007bff);
        color: var(--primary-color, #007bff);
        background: transparent;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background: var(--primary-color, #007bff);
        color: white;
    }

    .gap-2 {
        gap: 8px;
    }

    .widget__filter--btn__text {
        transition: all 0.3s ease;
    }
</style>
@endsection

@section('script')
    <script type="text/javascript">
        function filter(){
            $('#search-form').submit();
        }
        function rangefilter(arg){
            $('input[name=min_price]').val(arg[0]);
            $('input[name=max_price]').val(arg[1]);
            filter();
        }

        function setQuickPriceRange(min, max) {
            $('input[name=min_price]').val(min);
            $('input[name=max_price]').val(max || '');
            filter();
        }

        function filterByBrand(brandId, brandSlug) {
            // Add brand parameter to the current URL
            let currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('brand', brandSlug);
            window.location.href = currentUrl.toString();
        }

        function removeFilter(filterType) {
            switch(filterType) {
                case 'price':
                    $('input[name="min_price"]').val('');
                    $('input[name="max_price"]').val('');
                    break;
                case 'rating':
                    $('input[name="rating_filter"]').prop('checked', false);
                    break;
                case 'discount':
                    $('input[name="discount_filter"]').prop('checked', false);
                    break;
                case 'availability':
                    $('input[name="availability_filter"]').prop('checked', false);
                    break;
                case 'seller':
                    $('input[name="seller_type_filter"]').prop('checked', false);
                    break;
                case 'deal':
                    $('input[name="deal_type_filter"]').prop('checked', false);
                    break;
            }
            filter();
        }
        function selectShops() {
            // Set the select element to 'shops' and trigger the filter function
            document.getElementsByName('search_by')[0].value = 'shops';
            filter();
        }

        function clearAllFilters() {
            // Clear all filter inputs
            $('input[name="min_price"]').val('');
            $('input[name="max_price"]').val('');
            $('input[name="rating_filter"]').prop('checked', false);
            $('input[name="discount_filter"]').prop('checked', false);
            $('input[name="availability_filter"]').prop('checked', false);
            $('input[name="delivery_filter"]').prop('checked', false);
            $('input[name="seller_type_filter"]').prop('checked', false);
            $('input[name="deal_type_filter"]').prop('checked', false);
            $('input[name="review_count_filter"]').prop('checked', false);
            $('input[name="color"]').prop('checked', false);
            $('input[name="selected_attribute_values[]"]').prop('checked', false);
            $('select[name="sort_by"]').val('');

            // Submit the form to apply cleared filters
            filter();
        }

        // Show active filter count
        function updateFilterCount() {
            let activeFilters = 0;

            // Count active filters
            if ($('input[name="min_price"]').val() || $('input[name="max_price"]').val()) activeFilters++;
            if ($('input[name="rating_filter"]:checked').length) activeFilters++;
            if ($('input[name="discount_filter"]:checked').length) activeFilters++;
            if ($('input[name="availability_filter"]:checked').length) activeFilters++;
            if ($('input[name="delivery_filter"]:checked').length) activeFilters++;
            if ($('input[name="seller_type_filter"]:checked').length) activeFilters++;
            if ($('input[name="deal_type_filter"]:checked').length) activeFilters++;
            if ($('input[name="review_count_filter"]:checked').length) activeFilters++;
            if ($('input[name="color"]:checked').length) activeFilters++;
            if ($('input[name="selected_attribute_values[]"]:checked').length) activeFilters++;

            // Update filter button text
            if (activeFilters > 0) {
                $('.widget__filter--btn__text').text('{{ translate("Filters") }} (' + activeFilters + ')');
            } else {
                $('.widget__filter--btn__text').text('{{ translate("Filters") }}');
            }
        }

        // Update filter count on page load and when filters change
        $(document).ready(function() {
            updateFilterCount();

            // Update count when any filter changes
            $('input[type="checkbox"], input[type="radio"], input[type="number"], select').on('change', function() {
                setTimeout(updateFilterCount, 100);
            });
        });
    </script>
@endsection
