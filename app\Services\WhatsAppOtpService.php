<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppOtpService
{
    protected $apiToken;
    protected $phoneNumberId;
    protected $templateId;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiToken = env('WHATSAPP_API_TOKEN', '6723|qSDJOCP28LCAVLVl1ik4QzkhlQ9oMTGscJLWzJxB');
        $this->phoneNumberId = env('WHATSAPP_PHONE_NUMBER_ID', '383709501489036');
        $this->templateId = env('WHATSAPP_OTP_TEMPLATE_ID', '122343');
        $this->baseUrl = 'https://botsailor.com/api/v1/whatsapp/send/template';
    }

    /**
     * Send OTP via WhatsApp
     *
     * @param string $phoneNumber
     * @param string $otp
     * @return bool
     */
    public function sendOtp($phoneNumber, $otp)
    {
        try {
            // Format phone number (remove + and any spaces)
            $formattedPhone = preg_replace('/[^0-9+]/', '', $phoneNumber);
            
            $data = [
                'apiToken' => $this->apiToken,
                'phone_number_id' => $this->phoneNumberId,
                'template_id' => $this->templateId,
                'phone_number' => $formattedPhone,
                'templateVariable-otp-1' => $otp
            ];

            $response = Http::asForm()->post($this->baseUrl, $data);

            if ($response->successful()) {
                Log::info('WhatsApp OTP sent successfully', [
                    'phone' => $formattedPhone,
                    'otp' => $otp
                ]);
                return true;
            } else {
                Log::error('WhatsApp OTP failed', [
                    'phone' => $formattedPhone,
                    'response' => $response->body(),
                    'status' => $response->status()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('WhatsApp OTP exception', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if WhatsApp OTP is enabled
     *
     * @return bool
     */
    public function isEnabled()
    {
        return !empty($this->apiToken) && !empty($this->phoneNumberId) && !empty($this->templateId);
    }
}
